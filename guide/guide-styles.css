/**
 * 新手引导系统 - 专门样式文件
 * 基于 guide card.html 的现代设计升级版本
 * 包含高级视觉效果和主题适配
 */

/* 引导系统专用CSS变量 */
:root {
    /* 引导专用色彩 - 现代毛玻璃风格 */
    --guide-overlay-bg: rgba(45, 55, 72, 0.8);
    --guide-card-bg: rgba(45, 55, 72, 0.6);
    --guide-highlight-color: rgba(124, 58, 237, 0.8);
    --guide-highlight-glow: rgba(124, 58, 237, 0.4);
    --guide-tooltip-bg: rgba(45, 55, 72, 0.6);
    --guide-tooltip-shadow: rgba(0, 0, 0, 0.25);
    --guide-tooltip-border: rgba(255, 255, 255, 0.08);
    
    /* 引导动画配置 */
    --guide-animation-duration: 0.3s;
    --guide-animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
    --guide-highlight-animation: 2s;
    
    /* 引导层级 */
    --guide-overlay-z: 9999;
    --guide-highlight-z: 10000;
    --guide-tooltip-z: 10001;
    
    /* 引导间距 */
    --guide-tooltip-padding: 24px;
    --guide-tooltip-border-radius: 16px;
    --guide-highlight-padding: 8px;
    --guide-arrow-size: 8px;
    
    /* 现代配色方案 */
    --guide-primary-gradient: linear-gradient(135deg, #7c3aed, #6366f1);
    --guide-secondary-gradient: linear-gradient(135deg, #2d3748, #1a202c);
    --guide-text-primary: rgba(255, 255, 255, 0.95);
    --guide-text-secondary: rgba(255, 255, 255, 0.75);
    --guide-text-tertiary: rgba(255, 255, 255, 0.5);
}

/* 主要引导容器 - 毛玻璃效果 */
.guide-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--guide-overlay-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    z-index: var(--guide-overlay-z);
    opacity: 0;
    transition: opacity var(--guide-animation-duration) var(--guide-animation-easing);
    cursor: pointer;
}

.guide-overlay.active {
    opacity: 1;
}

/* 高亮效果 - 更现代的光晕 */
.guide-highlight {
    position: absolute;
    border-radius: var(--guide-tooltip-border-radius);
    box-shadow: 
        0 0 0 4px var(--guide-highlight-color),
        0 4px 24px rgba(124, 58, 237, 0.3);
    animation: guideHighlight var(--guide-highlight-animation) infinite;
    z-index: var(--guide-highlight-z);
    transition: all var(--guide-animation-duration) var(--guide-animation-easing);
    pointer-events: none;
}

@keyframes guideHighlight {
    0%, 100% { 
        box-shadow: 
            0 0 0 4px var(--guide-highlight-color),
            0 4px 24px rgba(124, 58, 237, 0.3);
        transform: scale(1);
    }
    50% { 
        box-shadow: 
            0 0 0 8px var(--guide-highlight-glow),
            0 8px 48px rgba(124, 58, 237, 0.5);
        transform: scale(1.02);
    }
}

/* 脉冲高亮效果（用于重点步骤） */
.guide-highlight.pulse {
    animation: guidePulse 1.5s infinite;
}

@keyframes guidePulse {
    0% {
        box-shadow: 
            0 0 0 4px var(--guide-highlight-color),
            0 4px 24px rgba(124, 58, 237, 0.3);
        opacity: 1;
    }
    70% {
        box-shadow: 
            0 0 0 12px var(--guide-highlight-glow),
            0 8px 64px rgba(124, 58, 237, 0.7);
        opacity: 0.8;
    }
    100% {
        box-shadow: 
            0 0 0 4px var(--guide-highlight-color),
            0 4px 24px rgba(124, 58, 237, 0.3);
        opacity: 1;
    }
}

/* 提示框主体 - 现代毛玻璃卡片 */
.guide-tooltip {
    position: fixed;
    background: var(--guide-tooltip-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--guide-tooltip-border);
    border-radius: var(--guide-tooltip-border-radius);
    padding: var(--guide-tooltip-padding);
    box-shadow: 
        0 4px 24px var(--guide-tooltip-shadow),
        0 8px 64px rgba(0, 0, 0, 0.12),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    max-width: 480px;
    min-width: 300px;
    z-index: var(--guide-tooltip-z);
    opacity: 0;
    transform: scale(0.8) translateY(10px);
    transition: all var(--guide-animation-duration) var(--guide-animation-easing);
    cursor: default;
    color: var(--guide-text-primary);
}

.guide-tooltip.active {
    opacity: 1;
    transform: scale(1) translateY(0);
}

/* 提示框箭头 - 与毛玻璃效果匹配 */
.guide-tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border: var(--guide-arrow-size) solid transparent;
    z-index: var(--guide-tooltip-z);
    filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.15));
}

.guide-tooltip-arrow.top {
    top: calc(-2 * var(--guide-arrow-size));
    left: 50%;
    transform: translateX(-50%);
    border-bottom-color: var(--guide-tooltip-bg);
}

.guide-tooltip-arrow.bottom {
    bottom: calc(-2 * var(--guide-arrow-size));
    left: 50%;
    transform: translateX(-50%);
    border-top-color: var(--guide-tooltip-bg);
}

.guide-tooltip-arrow.left {
    left: calc(-2 * var(--guide-arrow-size));
    top: 50%;
    transform: translateY(-50%);
    border-right-color: var(--guide-tooltip-bg);
}

.guide-tooltip-arrow.right {
    right: calc(-2 * var(--guide-arrow-size));
    top: 50%;
    transform: translateY(-50%);
    border-left-color: var(--guide-tooltip-bg);
}

/* 提示框头部 - 现代化设计 */
.guide-header {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 24px;
}

.guide-icon {
    background: var(--guide-primary-gradient);
    width: 64px;
    height: 64px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 24px;
    color: white;
    box-shadow: 0 4px 16px rgba(124, 58, 237, 0.3);
}

.guide-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--guide-text-primary);
    margin: 0;
    flex: 1;
    line-height: 1.3;
    min-width: 0;
}

.guide-close {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 32px;
    height: 32px;
    border: none;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.guide-close:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: scale(1.05);
}

.guide-close::before,
.guide-close::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 2px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 1px;
}

.guide-close::before {
    transform: rotate(45deg);
}

.guide-close::after {
    transform: rotate(-45deg);
}

/* 提示框内容 */
.guide-content {
    color: var(--guide-text-secondary);
    line-height: 1.6;
    margin-bottom: 24px;
    font-size: 14px;
}

.guide-content strong {
    color: var(--guide-text-primary);
    font-weight: 600;
}

.guide-content i {
    margin-right: 8px;
    color: #7c3aed;
}

/* 提示框底部 */
.guide-footer {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* 进度条 - 现代设计 */
.guide-progress {
    margin-bottom: 8px;
}

.guide-progress-text {
    font-size: 12px;
    color: var(--guide-text-tertiary);
    margin-bottom: 8px;
}

.guide-progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
}

.guide-progress-fill {
    height: 100%;
    background: var(--guide-primary-gradient);
    border-radius: 2px;
    transition: width 0.3s ease;
    position: relative;
}

.guide-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 按钮组 - 现代化布局 */
.guide-buttons {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

/* 按钮样式 - 基于 guide card.html 的设计 */
.guide-btn {
    padding: 10px 20px;
    border-radius: 8px;
    border: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: inherit;
    position: relative;
    overflow: hidden;
}

.guide-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.guide-btn:hover::before {
    left: 100%;
}

.guide-btn-primary {
    background: var(--guide-primary-gradient);
    color: white;
    box-shadow: 0 2px 8px rgba(124, 58, 237, 0.3);
}

.guide-btn-primary:hover {
    background: linear-gradient(135deg, #7c3aed, #5b21b6);
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(124, 58, 237, 0.4);
}

.guide-btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(124, 58, 237, 0.3);
}

.guide-btn-secondary {
    background: rgba(255, 255, 255, 0.08);
    color: var(--guide-text-secondary);
    border: 1px solid rgba(255, 255, 255, 0.12);
}

.guide-btn-secondary:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.2);
    color: var(--guide-text-primary);
    transform: translateY(-1px);
}

/* 跳过链接 */
.guide-keyboard-hint {
    color: var(--guide-text-tertiary);
    font-size: 11px;
    text-align: center;
    margin-top: 8px;
}

.guide-skip-link {
    color: var(--guide-text-tertiary);
    text-decoration: underline;
    font-size: 12px;
    cursor: pointer;
    margin-left: auto;
    transition: color 0.2s ease;
}

.guide-skip-link:hover {
    color: var(--guide-text-secondary);
}

/* 特殊步骤样式 */
.guide-step-welcome .guide-tooltip {
    background: var(--guide-secondary-gradient);
    border: 1px solid rgba(124, 58, 237, 0.3);
}

.guide-step-welcome .guide-title,
.guide-step-complete .guide-title {
    background: var(--guide-primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.guide-step-welcome .guide-progress,
.guide-step-complete .guide-progress {
    display: none;
}

.guide-step-welcome .guide-close,
.guide-step-complete .guide-close {
    background: rgba(124, 58, 237, 0.1);
}

.guide-step-welcome .guide-close:hover,
.guide-step-complete .guide-close:hover {
    background: rgba(124, 58, 237, 0.2);
}

.guide-step-complete .guide-tooltip {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(124, 58, 237, 0.1));
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.guide-step-feature .guide-tooltip {
    border: 2px solid rgba(124, 58, 237, 0.4);
}

.guide-step-feature .guide-icon {
    background: linear-gradient(135deg, #10b981, #059669);
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
}

.guide-step-action .guide-tooltip {
    border: 2px solid rgba(245, 158, 11, 0.4);
}

.guide-step-action .guide-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    box-shadow: 0 4px 16px rgba(245, 158, 11, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .guide-tooltip {
        max-width: calc(100vw - 32px);
        min-width: calc(100vw - 32px);
        margin: 16px;
        padding: 20px;
    }
    
    .guide-header {
        gap: 12px;
    }
    
    .guide-icon {
        width: 56px;
        height: 56px;
        font-size: 20px;
    }
    
    .guide-title {
        font-size: 18px;
    }
    
    .guide-content {
        font-size: 14px;
        margin-bottom: 20px;
    }
    
    .guide-buttons {
        flex-direction: column;
        align-items: stretch;
    }
    
    .guide-btn {
        width: 100%;
        justify-content: center;
    }
    
    .guide-skip-link {
        margin-left: 0;
        text-align: center;
        margin-top: 8px;
    }
    
    .guide-highlight {
        animation: guideMobileHighlight 2s infinite;
    }
    
    @keyframes guideMobileHighlight {
        0%, 100% { 
            box-shadow: 0 0 0 3px var(--guide-highlight-color);
            transform: scale(1);
        }
        50% { 
            box-shadow: 0 0 0 6px var(--guide-highlight-glow);
            transform: scale(1.01);
        }
    }
}

@media (max-width: 480px) {
    .guide-tooltip {
        padding: 16px;
        border-radius: 12px;
    }
    
    .guide-title {
        font-size: 16px;
    }
    
    .guide-content {
        font-size: 13px;
    }
    
    .guide-btn {
        padding: 12px 16px;
        font-size: 14px;
    }
}

/* 高对比度支持 */
@media (prefers-contrast: high) {
    .guide-overlay {
        background: rgba(0, 0, 0, 0.8);
    }
    
    .guide-tooltip {
        border: 2px solid #ffffff;
        background: rgba(0, 0, 0, 0.9);
    }
    
    .guide-highlight {
        box-shadow: 0 0 0 4px #ffffff;
    }
    
    .guide-btn-primary {
        background: #ffffff;
        color: #000000;
    }
    
    .guide-btn-secondary {
        background: transparent;
        border: 2px solid #ffffff;
        color: #ffffff;
    }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    .guide-tooltip,
    .guide-overlay,
    .guide-btn,
    .guide-progress-fill,
    .guide-close {
        transition: none;
        animation: none;
    }
    
    .guide-highlight {
        animation: none;
        box-shadow: 0 0 0 4px var(--guide-highlight-color);
    }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --guide-tooltip-bg: rgba(31, 41, 55, 0.9);
        --guide-text-primary: rgba(255, 255, 255, 0.95);
        --guide-text-secondary: rgba(255, 255, 255, 0.75);
        --guide-text-tertiary: rgba(255, 255, 255, 0.5);
    }
    
    .guide-tooltip {
        background: var(--guide-tooltip-bg);
        border: 1px solid rgba(75, 85, 99, 0.3);
    }
    
    .guide-title {
        color: var(--guide-text-primary);
    }
    
    .guide-content {
        color: var(--guide-text-secondary);
    }
    
    .guide-content strong {
        color: var(--guide-text-primary);
    }
    
    .guide-btn-secondary {
        background: rgba(75, 85, 99, 0.3);
        border: 1px solid rgba(107, 114, 128, 0.3);
        color: var(--guide-text-secondary);
    }
    
    .guide-btn-secondary:hover {
        background: rgba(75, 85, 99, 0.5);
        color: var(--guide-text-primary);
    }
    
    .guide-close {
        color: var(--guide-text-tertiary);
    }
    
    .guide-close:hover {
        background: rgba(75, 85, 99, 0.3);
        color: var(--guide-text-secondary);
    }
}

/* 打印样式 */
@media print {
    .guide-overlay,
    .guide-tooltip,
    .guide-highlight {
        display: none !important;
    }
}

/* 无障碍支持 */
.guide-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 特殊效果 - 彩虹边框（用于特殊场合） */
.guide-tooltip.rainbow-border {
    border: 2px solid;
    border-image: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff) 1;
}

.guide-tooltip.rainbow-border::before {
    content: '';
    position: absolute;
    inset: -2px;
    padding: 2px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    animation: rainbowRotate 3s linear infinite;
}

@keyframes rainbowRotate {
    to { transform: rotate(360deg); }
}

/* 加载状态 */
.guide-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #7c3aed;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 状态指示器 */
.guide-status-success { color: #10b981 !important; }
.guide-status-error { color: #ef4444 !important; }
.guide-status-warning { color: #f59e0b !important; }

/* 辅助类 */
.text-blue-500 { color: #3b82f6 !important; }
.text-green-500 { color: #10b981 !important; }
.text-purple-500 { color: #8b5cf6 !important; }
.text-orange-500 { color: #f59e0b !important; }
.text-red-500 { color: #ef4444 !important; }
.text-yellow-500 { color: #eab308 !important; }
.text-gray-500 { color: #6b7280 !important; }
.text-blue-600 { color: #2563eb !important; }
.text-orange-600 { color: #ea580c !important; }
.text-purple-600 { color: #7c3aed !important; }
.text-green-600 { color: #059669 !important; } 