/* =================================================================
   用户引导系统 (Driver.js) 样式彻底重构
   版本: 2.0
   作者: Gemini AI Assistant
   目标: 现代化、主题兼容、响应式、美观
   ================================================================= */

/* 核心变量定义 (可覆盖) */
:root {
    --guide-accent-color: var(--primary-color, #007bff);
    --guide-background: var(--surface-elevated, #ffffff);
    --guide-text-primary: var(--text-primary, #212529);
    --guide-text-secondary: var(--text-secondary, #6c757d);
    --guide-border-color: var(--border-light, #dee2e6);
    --guide-shadow-color: var(--shadow-color, rgba(0, 0, 0, 0.1));
    --guide-radius: 12px;
    --guide-arrow-size: 10px;
    --guide-animation-duration: 0.3s;
    --guide-animation-easing: cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* 深色主题适配 */
[data-theme="professional"] {
    --guide-shadow-color: rgba(0, 0, 0, 0.4);
}

/* 外星人主题适配 */
[data-theme="alien"] {
    --guide-accent-color: #00ff99;
    --guide-shadow-color: rgba(0, 255, 153, 0.2);
}


/* =======================================
   1. 遮罩层 (Overlay)
   ======================================= */
.driver-overlay {
    background-color: #000 !important;
    opacity: 0.5 !important; /* 降低不透明度，不再遮挡内容 */
    -webkit-backdrop-filter: blur(4px) !important; /* 背景模糊效果 */
    backdrop-filter: blur(4px) !important;
}


/* =======================================
   2. 引导框容器 (Popover)
   ======================================= */
.driver-popover {
    background: var(--guide-background) !important;
    color: var(--guide-text-primary) !important;
    border-radius: var(--guide-radius) !important;
    box-shadow: 0 8px 32px -4px var(--guide-shadow-color) !important;
    padding: 0 !important; /* 内部区域自己管理padding */
    border: 1px solid var(--guide-border-color) !important;
    max-width: 380px !important;
    min-width: 320px !important;
    transition: all var(--guide-animation-duration) var(--guide-animation-easing) !important;
    animation: guidePopoverFadeIn var(--guide-animation-duration) var(--guide-animation-easing) !important;
}

/* 修复之前错误的定位逻辑，使用复合选择器 */
.driver-popover.guide-step-welcome,
.driver-popover.guide-step-complete {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
}


/* =======================================
   3. 引导框内部结构
   ======================================= */

/* 标题区域 */
.driver-popover-title {
    font-size: 1.25rem !important; /* 20px */
    font-weight: 700 !important;
    color: var(--guide-text-primary) !important;
    padding: 1.5rem 1.5rem 0.75rem !important; /* 24px 24px 12px */
    display: flex;
    align-items: center;
    gap: 0.75rem;
    border-bottom: 1px solid var(--guide-border-color) !important;
    margin: 0 !important;
}

/* 正文区域 */
.driver-popover-description {
    padding: 1rem 1.5rem !important; /* 16px 24px */
    font-size: 0.95rem !important;
    line-height: 1.6 !important;
    color: var(--guide-text-secondary) !important;
    margin-bottom: 0 !important;
}

.driver-popover-description strong {
    color: var(--guide-accent-color) !important;
    font-weight: 600;
}

.driver-popover-description ul {
    margin-top: 1rem;
    padding-left: 1.25rem;
}

/* 页脚区域 */
.driver-popover-footer {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 0.75rem 1.5rem !important; /* 12px 24px */
    background-color: color-mix(in srgb, var(--guide-background), var(--guide-border-color) 5%) !important;
    border-top: 1px solid var(--guide-border-color) !important;
    border-radius: 0 0 var(--guide-radius) var(--guide-radius) !important;
}

/* =======================================
   4. 页脚元素
   ======================================= */

/* 进度文本 */
.driver-popover-progress-text {
    font-size: 0.8rem !important;
    color: var(--guide-text-secondary) !important;
    font-weight: 500 !important;
    white-space: nowrap !important;
    margin-right: auto !important; /* 将其推到最左边 */
}

/* 按钮容器 */
.driver-popover-navigation-btns {
    display: flex !important;
    gap: 0.75rem !important;
    flex-shrink: 0 !important;
}

/* 所有按钮的基类 */
.driver-popover-btn {
    border: none !important;
    border-radius: 8px !important;
    padding: 0.6rem 1rem !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    white-space: nowrap !important;
    line-height: 1 !important;
}

/* 下一步 / 完成按钮 */
.driver-popover-next-btn,
.driver-popover-done-btn {
    background-color: var(--guide-accent-color) !important;
    color: var(--text-inverse, #fff) !important;
}
.driver-popover-next-btn:hover,
.driver-popover-done-btn:hover {
    filter: brightness(1.1);
    box-shadow: 0 4px 12px -2px color-mix(in srgb, var(--guide-accent-color), transparent 50%) !important;
    transform: translateY(-2px);
}

/* 上一步按钮 */
.driver-popover-prev-btn {
    background-color: transparent !important;
    color: var(--guide-text-secondary) !important;
    box-shadow: inset 0 0 0 1px var(--guide-border-color) !important;
}
.driver-popover-prev-btn:hover {
    background-color: var(--guide-border-color) !important;
    color: var(--guide-text-primary) !important;
}

/* 关闭按钮 (右上角) */
.driver-popover-close-btn {
    position: absolute !important;
    top: 0.75rem !important;
    right: 0.75rem !important;
    background: transparent !important;
    color: var(--guide-text-secondary) !important;
    border: none !important;
    font-size: 1.5rem !important;
    padding: 0.25rem !important;
    line-height: 1 !important;
    opacity: 0.7;
    transition: all var(--guide-animation-duration) ease !important;
}
.driver-popover-close-btn:hover {
    opacity: 1;
    color: var(--error-color, #dc3545) !important;
    transform: rotate(90deg);
}

/* =======================================
   5. 箭头
   ======================================= */
.driver-popover-arrow-side-top {
    border-bottom-color: var(--guide-border-color) !important;
}
.driver-popover-arrow-side-bottom {
    border-top-color: var(--guide-border-color) !important;
}
.driver-popover-arrow-side-left {
    border-right-color: var(--guide-border-color) !important;
}
.driver-popover-arrow-side-right {
    border-left-color: var(--guide-border-color) !important;
}

.driver-popover-arrow-side-top::after {
    border-bottom-color: var(--guide-background) !important;
}
.driver-popover-arrow-side-bottom::after {
    border-top-color: var(--guide-background) !important;
}
.driver-popover-arrow-side-left::after {
    border-right-color: var(--guide-background) !important;
}
.driver-popover-arrow-side-right::after {
    border-left-color: var(--guide-background) !important;
}


/* =======================================
   6. 动画
   ======================================= */
@keyframes guidePopoverFadeIn {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
} 